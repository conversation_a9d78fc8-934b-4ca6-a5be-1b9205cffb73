// Controller for Client API

const clientServices = require('../../../services/api/v2/client-services');
const {
  siteDiscoveryQuerySchemaAPI,
  createWorkOrderRequestSchemaAPI,
  updateWorkOrderRequestSchemaAPI,
  listWorkOrdersQuerySchemaAPI,
  getWorkOrderByExternalIdSchemaAPI,
  sendMessageToVendorSchemaAPI,
} = require('../../schemas/client');
const { z } = require('zod');


const getWorkOrderByExternalId = async (req, res) => {
  const clientVendorId = req.headers.vendorId;
  const isClientViewEnabled = req.headers.isClientViewEnabled;

  if (isClientViewEnabled !== true && isClientViewEnabled !== 'true') {
    return res.status(403).json({ message: 'Forbidden: ClientView not enabled for this vendor.' });
  }

  try {
    const { external_wo_id } = await getWorkOrderByExternalIdSchemaAPI(clientVendorId).parseAsync(req.params);
    const workOrder = await clientServices.getWorkOrderByExternalId(clientVendorId, external_wo_id);
    if (!workOrder) {
      return res.status(404).json({ message: 'Work order not found.' });
    }
    res.status(200).json(workOrder);
  } catch (error) {
    if (error instanceof z.ZodError) {
      res.status(400).json({ error: error.errors });
    } else {
      console.error('Error in getWorkOrderByExternalId controller:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
};

const discoverTrades = async (req, res) => {
  const clientVendorId = req.headers.vendorId;
  const isClientViewEnabled = req.headers.isClientViewEnabled;

  if (isClientViewEnabled !== true && isClientViewEnabled !== 'true') {
    return res.status(403).json({ message: 'Forbidden: ClientView not enabled for this vendor.' });
  }

  try {
    const trades = await clientServices.discoverTrades(clientVendorId);
    res.status(200).json(trades);
  } catch (error) {
    console.error('Error in discoverTrades controller:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};


const discoverServiceTypes = async (req, res) => {
  const clientVendorId = req.headers.vendorId;
  const isClientViewEnabled = req.headers.isClientViewEnabled;

  if (isClientViewEnabled !== true && isClientViewEnabled !== 'true') {
    return res.status(403).json({ message: 'Forbidden: ClientView not enabled for this vendor.' });
  }

  try {
    const serviceTypes = await clientServices.discoverServiceTypes(clientVendorId);
    res.status(200).json(serviceTypes);
  } catch (error) {
    console.error('Error in discoverServiceTypes controller:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};


const discoverVendors = async (req, res) => {
 
  const clientVendorId = req.headers.vendorId;
  const isClientViewEnabled = req.headers.isClientViewEnabled;
  if (isClientViewEnabled !== true && isClientViewEnabled !== 'true') {
    return res.status(403).json({ message: 'Forbidden: ClientView not enabled for this vendor.' });
  }

  try {
    const vendors = await clientServices.discoverVendors(clientVendorId);
    res.status(200).json(vendors);
  } catch (error) {
    console.error('Error in discoverVendors controller:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};


const discoverSites = async (req, res) => {
  const clientVendorId = req.headers.vendorId;
  const isClientViewEnabled = req.headers.isClientViewEnabled;

  if (isClientViewEnabled !== true && isClientViewEnabled !== 'true') {
    return res.status(403).json({ message: 'Forbidden: ClientView not enabled for this vendor.' });
  }

  try {
    const { sitefotos_internal_vendor_id = null} = await siteDiscoveryQuerySchemaAPI(clientVendorId).parseAsync(req.query);
    console.log(sitefotos_internal_vendor_id, req.query);
    const sites = await clientServices.discoverSites(clientVendorId, sitefotos_internal_vendor_id);
    res.status(200).json(sites);
  } catch (error) {
    if (error instanceof z.ZodError) {
      res.status(400).json({ error: error.errors });
    } else {
      console.error('Error in discoverSites controller:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
};


const createWorkOrder = async (req, res) => {
  const clientVendorId = req.headers.vendorId;
  const isClientViewEnabled = req.headers.isClientViewEnabled;

  if (isClientViewEnabled !== true && isClientViewEnabled !== 'true') {
    return res.status(403).json({ message: 'Forbidden: ClientView not enabled for this vendor.' });
  }
  
  try {
     console.log(req.body);
    const parsedBody = await createWorkOrderRequestSchemaAPI(clientVendorId).parseAsync(req.body);
   

    const result = await clientServices.createClientWorkOrder(clientVendorId, parsedBody);
    
    res.status(201).json(result);
  } catch (error) {
    console.error('Error in createWorkOrder controller:', error);
    if (error instanceof z.ZodError) {
        return res.status(400).json({ error: error.errors });
    }
   
    res.status(500).json({ message: 'Internal server error' });
  }
};


const updateWorkOrder = async (req, res) => {
  const clientVendorId = req.headers.vendorId;
  const isClientViewEnabled = req.headers.isClientViewEnabled;
  const { sitefotos_work_order_id } = req.params;

  if (isClientViewEnabled !== true && isClientViewEnabled !== 'true') {
    return res.status(403).json({ message: 'Forbidden: ClientView not enabled for this vendor.' });
  }

  try {
    // Use the new schema factory, passing clientVendorId and the raw sitefotos_work_order_id from params
    const schema = updateWorkOrderRequestSchemaAPI(clientVendorId, sitefotos_work_order_id);
    const parsedBody = await schema.parseAsync(req.body); // Use parseAsync for async superRefine

    // The sitefotos_work_order_id from params is already validated (format and access) by the schema's superRefine
    // No need to decode it again here if the service expects the encoded ID.
    // If service expects decoded, then decode it here after schema validation.
    // Based on current service, it decodes it. So we pass the encoded one.
    const result = await clientServices.updateClientWorkOrder(clientVendorId, sitefotos_work_order_id, parsedBody);
    
    res.status(200).json(result);
  } catch (error) {
    console.error('Error in updateWorkOrder controller:', error);
    if (error instanceof z.ZodError) {
        // Errors from superRefine (like access denied) will also be caught here
        return res.status(400).json({ message: 'Validation error', errors: error.errors });
    }
    // Specific error messages from the service layer can be handled if needed,
    // but schema should catch most input/access issues.
    if (error.message.includes('not found or not accessible') || error.message.startsWith('Invalid sitefotos_work_order_id')) {
        // This case should ideally be caught by the schema's superRefine now.
        return res.status(404).json({ message: error.message });
    }
    if (error.message.startsWith('Invalid status') || error.message.startsWith('No valid fields')) {
        // "No valid fields" should be caught by schema's refine. "Invalid status" might be service-level if complex.
        return res.status(400).json({ message: error.message });
    }
    res.status(500).json({ message: 'Internal server error' });
  }
};

const listWorkOrders = async (req, res) => {
  const clientVendorIdHeader = req.headers.vendorId;
  const isClientViewEnabled = req.headers.isClientViewEnabled;

  if (isClientViewEnabled !== true && isClientViewEnabled !== 'true') {
    return res.status(403).json({ message: 'Forbidden: ClientView not enabled for this vendor.' });
  }

  const clientVendorId = parseInt(clientVendorIdHeader, 10);
  if (isNaN(clientVendorId)) {
      return res.status(400).json({ message: 'Invalid or missing vendorId header.' });
  }

  try {
    // Pass the parsed integer clientVendorId to the schema factory
    const validatedQueryParams = await listWorkOrdersQuerySchemaAPI(clientVendorId).parseAsync(req.query);
    const result = await clientServices.listClientWorkOrders(clientVendorId, validatedQueryParams);

    const response = {
      data: result.workOrders,
      metadata: {
        pagination: {
          total_items: result.totalItems,
          total_pages: Math.ceil(result.totalItems / validatedQueryParams.limit),
          current_page: validatedQueryParams.page,
          page_size: validatedQueryParams.limit,
        }
      }
    };
    res.status(200).json(response);
  } catch (error) {
    if (error instanceof z.ZodError) {
      res.status(400).json({ message: 'Validation error', errors: error.errors });
    } else {
      console.error('Error in listWorkOrders controller:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }
};

const discoverWorkOrderFormTemplates = async (req, res) => {
  const clientVendorId = req.headers.vendorId;
  const isClientViewEnabled = req.headers.isClientViewEnabled;

  if (isClientViewEnabled !== true && isClientViewEnabled !== 'true') {
    return res.status(403).json({ message: 'Forbidden: ClientView not enabled for this vendor.' });
  }

  try {
    const templates = await clientServices.discoverWorkOrderFormTemplates(clientVendorId);
    res.status(200).json(templates);
  } catch (error) {
    console.error('Error in discoverWorkOrderFormTemplates controller:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

const sendMessageToVendor = async (req, res) => {
  const clientVendorId = req.headers.vendorId;
  const isClientViewEnabled = req.headers.isClientViewEnabled;

  if (isClientViewEnabled !== true && isClientViewEnabled !== 'true') {
    return res.status(403).json({ message: 'Forbidden: ClientView not enabled for this vendor.' });
  }

  try {
    const parsedBody = await sendMessageToVendorSchemaAPI(clientVendorId).parseAsync(req.body);
    const result = await clientServices.sendMessageToVendor(clientVendorId, parsedBody);

    res.status(200).json(result);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ message: 'Validation error', errors: error.errors });
    }

    console.error('Error in sendMessageToVendor controller:', error);

    // Handle specific error messages from the service layer
    if (error.message.includes('not found or not accessible') ||
        error.message.includes('Invalid sitefotos_work_order_id') ||
        error.message.includes('email address not found')) {
      return res.status(404).json({ message: error.message });
    }

    if (error.message.includes('Failed to send email')) {
      return res.status(500).json({ message: 'Failed to send message to vendor. Please try again.' });
    }

    res.status(500).json({ message: 'Internal server error' });
  }
};

module.exports = {
  discoverTrades,
  discoverServiceTypes,
  discoverVendors,
  discoverSites,
  createWorkOrder,
  updateWorkOrder,
  listWorkOrders, // Added new controller function
  getWorkOrderByExternalId,
  discoverWorkOrderFormTemplates,
  sendMessageToVendor, // Added new controller function
  // Export other controller functions here
};